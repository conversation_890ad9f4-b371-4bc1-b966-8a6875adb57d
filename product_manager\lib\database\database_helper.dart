import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../models/product.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'aylix.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE products(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_code TEXT NOT NULL UNIQUE,
        product_name TEXT NOT NULL,
        image_url TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )
    ''');
  }

  Future<int> insertProduct(Product product) async {
    final db = await database;
    try {
      return await db.insert(
        'products',
        product.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      print('Error inserting product: $e');
      rethrow;
    }
  }

  Future<List<Product>> getAllProducts() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return Product.fromMap(maps[i]);
      });
    } catch (e) {
      print('Error getting products: $e');
      return [];
    }
  }

  Future<Product?> getProductByCode(String productCode) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'products',
        where: 'product_code = ?',
        whereArgs: [productCode],
      );

      if (maps.isNotEmpty) {
        return Product.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Error getting product by code: $e');
      return null;
    }
  }

  Future<int> updateProduct(Product product) async {
    final db = await database;
    try {
      return await db.update(
        'products',
        product.toMap(),
        where: 'id = ?',
        whereArgs: [product.id],
      );
    } catch (e) {
      print('Error updating product: $e');
      rethrow;
    }
  }

  Future<int> deleteProduct(int id) async {
    final db = await database;
    try {
      return await db.delete(
        'products',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      print('Error deleting product: $e');
      rethrow;
    }
  }

  Future<int> getProductCount() async {
    final db = await database;
    try {
      final result = await db.rawQuery('SELECT COUNT(*) FROM products');
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      print('Error getting product count: $e');
      return 0;
    }
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
